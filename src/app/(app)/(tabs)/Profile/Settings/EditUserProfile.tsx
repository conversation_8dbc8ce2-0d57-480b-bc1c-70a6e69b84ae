import React, {useEffect, useState, useRef} from 'react';
import {ActivityIndicator, Alert, ScrollView, Keyboard} from 'react-native';
import {Avatar, Button, Checkbox, Dialog, Input, Label, Spacer, Text, XStack, YStack} from 'tamagui';
import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {deleteUserAccount, getUserData, updateUserProfile, uploadImage} from '@/api/usersAPI';
import {useAuth} from '@/provider/AuthProvider';
import {FontAwesome5} from '@expo/vector-icons';
import * as ImagePicker from "expo-image-picker";
import {AxiosError} from "axios";
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import {Toast, useToast, useToastController} from '@tamagui/toast'
import MusicProviderSelector from '@/components/MusicProviderSelector';


export default function EditUserProfile() {
    const [musicProvider, setMusicProvider] = useState<string>('APPLE_MUSIC');
    const [isDeleteConfirmed, setIsDeleteConfirmed] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [firstName, setFirstName] = useState<string>('');
    const [lastName, setLastName] = useState<string>('');
    const [username, setUsername] = useState<string>('');
    const [bio, setBio] = useState<string>('');
    const [profileImage, setProfileImage] = useState<string | null>(null);
    const toast = useToastController()

    // Refs for text inputs
    const firstNameRef = useRef<any>(null);
    const lastNameRef = useRef<any>(null);
    const usernameRef = useRef<any>(null);
    const bioRef = useRef<any>(null);

    const {onLogout} = useAuth();

    const queryClient = useQueryClient();
    // Fetch user data using React Query
    const {data: userData, isLoading} = useQuery({
        queryKey: ['userData'],
        queryFn: getUserData,
    });

    useEffect(() => {
        if (userData) {
            setFirstName(userData.firstName);
            setLastName(userData.lastName);
            setUsername(userData.username);
            setBio(userData.bio);
            setMusicProvider(userData.musicProvider);
            setProfileImage(userData.profilePicture);
        }
    }, [userData]);

    const {mutateAsync: deleteAccount} = useMutation({
        mutationFn: deleteUserAccount,
        onSuccess: async () => {
            await onLogout!();
        },
    });

    const {mutateAsync: saveUserData} = useMutation({
        mutationFn: updateUserProfile,
        onSuccess: () => {
            toast.show('Profile updated successfully', {
                theme: 'green',
            });
            void queryClient.invalidateQueries({queryKey: ['userData']});
        },
        onError: (error) => {
            toast.show('Error updating profile', {
                theme: 'red',
            });
            console.log('Error updating profile:', error.message || error);
        },
    });

    const handleDeleteAccount = async () => {
        if (isDeleteConfirmed) {
            await deleteAccount();
            setIsDeleteDialogOpen(false);
        }
    };

    const handleSaveChanges = async () => {
        // Blur all inputs before saving
        firstNameRef.current?.blur();
        lastNameRef.current?.blur();
        usernameRef.current?.blur();
        bioRef.current?.blur();

        await saveUserData({
            firstName,
            lastName,
            username,
            bio,
            musicProvider,
        });
    };

    const handleInputBlur = () => {
        // Ensure proper cleanup of text input session
        Keyboard.dismiss();
    };

    const handleInputEndEditing = () => {
        // Additional cleanup for iOS text input session - handled per input
    };

    const pickImage = async () => {
        let result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [4, 3],
            quality: 1,
        });

        if (!result.canceled) {
            const uri = result.assets[0].uri
            setProfileImage(uri);
            uploadImageMutation.mutate({uri})
        }
    };

    const uploadImageMutation = useMutation({
        mutationFn: uploadImage,
        onSuccess: () => {
            // console.log("Image Upload Successful")
        },
        onError: (err: AxiosError) => {
            console.log(err)
            Alert.alert("Error", "An error occurred while uploading the image. Please try again later.");
        }
    })

    if (isLoading) {
        return (
            <YStack flex={1} justifyContent="center" alignItems="center">
                <ActivityIndicator size="large" color="#fed900"/>
            </YStack>
        );
    }

    return (
        <YStack flex={1}>
            <ScrollView contentContainerStyle={{alignItems: 'center', padding: 16}}
                        showsVerticalScrollIndicator={false}>
                <YStack ai="flex-start" jc="flex-start" w="100%" flex={1}>
                    <YStack ai="center" mt="$6" w="100%">
                        <YStack width={100} height={100} position="relative">
                            <Avatar circular size="$10" style={{marginBottom: 10}}>
                                {profileImage ? (
                                    <Avatar.Image
                                        accessibilityLabel="Profile Picture"
                                        src={profileImage}
                                    />
                                ) : (
                                    <Avatar.Fallback backgroundColor="$yellow10"/>
                                )}
                            </Avatar>
                            <YStack position="absolute" bottom={-10} right={-10}>
                                <Button size='$2' onPress={pickImage}
                                        icon={<MaterialIcons name="flip-camera-ios" size={24} color="white"/>}/>
                            </YStack>
                        </YStack>
                    </YStack>
                    <YStack mt="$6" w="100%">
                        <Text color="white" mb="$2">First name</Text>
                        <Input
                            ref={firstNameRef}
                            placeholder="Yuno"
                            value={firstName}
                            onChangeText={setFirstName}
                            onBlur={handleInputBlur}
                            onEndEditing={handleInputEndEditing}
                            blurOnSubmit={false}
                            returnKeyType="next"
                            onSubmitEditing={() => lastNameRef.current?.focus()}
                        />
                        <Spacer size={10}/>
                        <Text color="white" mb="$2">Last name</Text>
                        <Input
                            ref={lastNameRef}
                            placeholder="Arabsaid"
                            value={lastName}
                            onChangeText={setLastName}
                            onBlur={handleInputBlur}
                            onEndEditing={handleInputEndEditing}
                            blurOnSubmit={false}
                            returnKeyType="next"
                            onSubmitEditing={() => usernameRef.current?.focus()}
                        />
                    </YStack>
                    <YStack mt="$4" w="100%">
                        <Text color="white" mb="$2">Username</Text>
                        <Input
                            ref={usernameRef}
                            placeholder="maximax"
                            value={username}
                            onChangeText={(text) => setUsername(text.replace(/\s/g, ''))}
                            onBlur={handleInputBlur}
                            onEndEditing={handleInputEndEditing}
                            blurOnSubmit={false}
                            returnKeyType="next"
                            onSubmitEditing={() => bioRef.current?.focus()}
                        />
                    </YStack>
                    <YStack mt="$4" w="100%">
                        <Text color="white" mb="$2">Biografie (150 Character limit)</Text>
                        <Input
                            ref={bioRef}
                            placeholder="Biografie"
                            multiline
                            numberOfLines={4}
                            textAlignVertical="top"
                            value={bio}
                            onChangeText={setBio}
                            onBlur={handleInputBlur}
                            onEndEditing={handleInputEndEditing}
                            blurOnSubmit={false}
                            returnKeyType="done"
                        />
                        <Label textAlign="left" paddingBottom={10}>Select your music provider</Label>

                            <MusicProviderSelector musicProvider={musicProvider}
                                                   setMusicProvider={setMusicProvider}/>

                        <Spacer size={30}/>
                        <Button bg="white" w="100%" onPress={handleSaveChanges}>
                            <Text color="black">Save Changes</Text>
                        </Button>
                        <Spacer size={10}/>
                        <Button bg="#541B1F" w="100%" onPress={() => setIsDeleteDialogOpen(true)}>
                            Delete Account
                        </Button>
                    </YStack>
                </YStack>
            </ScrollView>

            <Dialog modal open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <Dialog.Portal>
                    <Dialog.Overlay key="overlay"/>
                    <Dialog.Content bordered elevate key="content">
                        <Dialog.Title>Are you absolutely sure?</Dialog.Title>
                        <Dialog.Description>
                            This action cannot be undone. This will permanently delete your account and remove your data
                            from our servers.
                        </Dialog.Description>
                        <XStack space="$2" alignItems="center">
                            <Checkbox checked={isDeleteConfirmed}
                                      onCheckedChange={(checked) => setIsDeleteConfirmed(checked === true)} id="terms"/>
                            <Label htmlFor="terms">I understand and want to delete my account</Label>
                        </XStack>
                        <Spacer size="$4"/>
                        <XStack space="$3" justifyContent="flex-end">
                            <Dialog.Close asChild>
                                <Button>Cancel</Button>
                            </Dialog.Close>
                            <Button theme="active" disabled={!isDeleteConfirmed} onPress={handleDeleteAccount}>
                                Delete Account
                            </Button>
                        </XStack>
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog>
        </YStack>
    );
}
