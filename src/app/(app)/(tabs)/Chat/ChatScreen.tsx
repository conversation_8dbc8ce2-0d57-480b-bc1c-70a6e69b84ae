import React, { useState, useRef } from 'react'
import {
    YStack,
    XStack,
    Text,
    ScrollView,
    Input,
    Button,
    styled,
    Avatar,
} from 'tamagui'
import { User, Message, Chat } from '@/types/ChatTypes'
import { useLocalSearchParams, router } from 'expo-router'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Keyboard } from 'react-native'
import {useHideTabBar} from "@/hooks/useHideTabBar";
import FontAwesome from '@expo/vector-icons/FontAwesome';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';


const MessageBubble = styled(YStack, {
    name: 'MessageBubble',
    maxWidth: '80%',
    padding: '$3',
    borderRadius: '$4',
    marginVertical: '$1',
    variants: {
        isOwn: {
            true: {
                backgroundColor: '#fed900',
                alignSelf: 'flex-end',
                borderBottomRightRadius: '$2',
            },
            false: {
                backgroundColor: '$gray2',
                alignSelf: 'flex-start',
                borderBottomLeftRadius: '$2',
            },
        },
    } as const,
})

interface MessageItemProps {
    message: Message
    isOwn: boolean
}

const MessageItem: React.FC<MessageItemProps> = ({ message, isOwn }) => {
    const formatTime = (date: Date) => {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }

    return (
        <MessageBubble isOwn={isOwn}>
            <Text
                fontSize="$4"
                color={isOwn ? '$black1' : '$white1'}
                lineHeight="$4"
            >
                {message.text}
            </Text>
            <Text
                fontSize="$2"
                color={isOwn ? '$black10' : '$white1'}
                alignSelf="flex-end"
                marginTop="$1"
                opacity={0.7}
            >
                {formatTime(message.timestamp)}
            </Text>
        </MessageBubble>
    )
}

// Mock data - same as in ChatListScreen for consistency
const mockUsers: User[] = [
    { id: '1', name: 'John Doe', avatar: 'https://i.pravatar.cc/150?img=1', isOnline: true },
    { id: '2', name: 'Jane Smith', avatar: 'https://i.pravatar.cc/150?img=2', isOnline: false },
    { id: '3', name: 'Mike Johnson', avatar: 'https://i.pravatar.cc/150?img=3', isOnline: true },
];

const mockChats: Chat[] = [
    {
        id: '1',
        user: mockUsers[0],
        lastMessage: { id: '1', text: 'Hey there!', senderId: '1', timestamp: new Date(), isRead: false },
        unreadCount: 2,
        lastActivity: new Date(),
    },
    {
        id: '2',
        user: mockUsers[1],
        lastMessage: { id: '2', text: 'Are you coming to the party?', senderId: '2', timestamp: new Date(Date.now() - 3600000), isRead: true },
        unreadCount: 0,
        lastActivity: new Date(Date.now() - 3600000),
    },
    {
        id: '3',
        user: mockUsers[2],
        lastMessage: { id: '3', text: 'Check out this new song!', senderId: '3', timestamp: new Date(Date.now() - 86400000), isRead: false },
        unreadCount: 1,
        lastActivity: new Date(Date.now() - 86400000),
    },
];

// Mock messages for each chat
const mockChatMessages: Record<string, Message[]> = {
    '1': [
        { id: '1', text: 'Hello!', senderId: '1', timestamp: new Date(Date.now() - 3600000), isRead: true },
        { id: '2', text: 'Hey there!', senderId: 'current-user', timestamp: new Date(Date.now() - 3000000), isRead: true },
        { id: '3', text: 'How are you?', senderId: '1', timestamp: new Date(Date.now() - 2400000), isRead: true },
        { id: '4', text: 'I\'m good, thanks!', senderId: 'current-user', timestamp: new Date(Date.now() - 1800000), isRead: true },
    ],
    '2': [
        { id: '1', text: 'Are you coming to the party?', senderId: '2', timestamp: new Date(Date.now() - 3600000), isRead: true },
        { id: '2', text: 'What time is it?', senderId: 'current-user', timestamp: new Date(Date.now() - 3000000), isRead: true },
        { id: '3', text: '8 PM at my place', senderId: '2', timestamp: new Date(Date.now() - 2400000), isRead: true },
        { id: '4', text: 'I\'ll be there!', senderId: 'current-user', timestamp: new Date(Date.now() - 1800000), isRead: true },
    ],
    '3': [
        { id: '1', text: 'Check out this new song!', senderId: '3', timestamp: new Date(Date.now() - 86400000), isRead: true },
        { id: '2', text: 'Which one?', senderId: 'current-user', timestamp: new Date(Date.now() - 82800000), isRead: true },
        { id: '3', text: 'The new one by that band we like', senderId: '3', timestamp: new Date(Date.now() - 79200000), isRead: true },
        { id: '4', text: 'I\'ll check it out!', senderId: 'current-user', timestamp: new Date(Date.now() - 75600000), isRead: true },
    ],
};

// Current user ID (in a real app, this would come from authentication)
const CURRENT_USER_ID = 'current-user';

export default function ChatScreen() {
    useHideTabBar();
    const { chatId } = useLocalSearchParams<{ chatId: string }>();
    const [inputText, setInputText] = useState('');
    const inputRef = useRef<any>(null);

    // Find the chat based on the chatId
    const chat = mockChats.find(c => c.id === chatId);
    const messages = mockChatMessages[chatId as string] || [];

    const handleBackPress = () => {
        router.back();
    };

    const handleSend = () => {
        if (inputText.trim()) {
            // In a real app, you would send the message to a backend
            console.log('Sending message:', inputText.trim());
            setInputText('');
            // Blur the input to properly cleanup the text input session
            inputRef.current?.blur();
        }
    };

    const handleInputBlur = () => {
        // Ensure proper cleanup of text input session
        Keyboard.dismiss();
    };

    const handleInputEndEditing = () => {
        // Additional cleanup for iOS text input session
        if (inputRef.current) {
            inputRef.current.blur();
        }
    };

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <YStack flex={1} backgroundColor="$background">
                <XStack
                    padding="$4"
                    backgroundColor="$background"
                    borderBottomWidth={1}
                    borderBottomColor="$borderColor"
                    alignItems="center"
                    gap="$3"
                >
                    <Button
                        size="$4"
                        circular
                        backgroundColor="transparent"
                        onPress={handleBackPress}
                    >
                        <FontAwesome5 name="arrow-left" size={24} color="white" />
                    </Button>

                    {chat && (
                        <>
                            <Avatar circular size="$5">
                                <Avatar.Image src={chat.user.avatar} />
                                <Avatar.Fallback backgroundColor="$gray3">
                                    <Text color="$color" fontSize="$5">
                                        {chat.user.name.charAt(0).toUpperCase()}
                                    </Text>
                                </Avatar.Fallback>
                            </Avatar>

                            <YStack flex={1}>
                                <Text fontSize="$6" fontWeight="600" color="$color">
                                    {chat.user.name}
                                </Text>
                                <Text fontSize="$3" color="$placeholderColor">
                                    {chat.user.isOnline ? 'Online' : 'Last seen recently'}
                                </Text>
                            </YStack>
                        </>
                    )}
                </XStack>

                <ScrollView
                    flex={1}
                    padding="$4"
                    contentContainerStyle={{ gap: '$2' }}
                >
                    {messages.map((message) => (
                        <MessageItem
                            key={message.id}
                            message={message}
                            isOwn={message.senderId === CURRENT_USER_ID}
                        />
                    ))}
                </ScrollView>

                <XStack
                    padding="$3"
                    backgroundColor="$background"
                    borderTopWidth={1}
                    borderTopColor="$borderColor"
                    alignItems="center"
                    gap="$3"
                >
                    <Input
                        ref={inputRef}
                        flex={1}
                        value={inputText}
                        onChangeText={setInputText}
                        placeholder="Type a message..."
                        placeholderTextColor="$placeholderColor"
                        backgroundColor="$gray2"
                        borderColor="$borderColor"
                        borderRadius="$5"
                        padding="$3"
                        color="$color"
                        onBlur={handleInputBlur}
                        onEndEditing={handleInputEndEditing}
                        blurOnSubmit={false}
                        returnKeyType="send"
                        onSubmitEditing={handleSend}
                    />
                    <Button
                        size="$5"
                        circular
                        backgroundColor="$primary"
                        onPress={handleSend}
                        disabled={!inputText.trim()}
                        opacity={inputText.trim() ? 1 : 0.5}
                    >
                        <FontAwesome name="send" size={24} color="white" />
                    </Button>
                </XStack>
            </YStack>
        </SafeAreaView>
    )
}
