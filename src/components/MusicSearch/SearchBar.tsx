import React, { useRef } from 'react';
import {Button, Input, YStack} from 'tamagui';
import { Keyboard } from 'react-native';

type SearchBarProps = {
    searchQuery: string;
    setSearchQuery: (text: string) => void;
    onCancel: () => void;
};

const SearchBar: React.FC<SearchBarProps> = ({searchQuery, setSearchQuery, onCancel}) => {
    const inputRef = useRef<any>(null);

    const handleInputBlur = () => {
        // Ensure proper cleanup of text input session
        Keyboard.dismiss();
    };

    const handleInputEndEditing = () => {
        // Additional cleanup for iOS text input session
        if (inputRef.current) {
            inputRef.current.blur();
        }
    };

    const handleCancel = () => {
        inputRef.current?.blur();
        onCancel();
    };

    return (
        <YStack
            flexDirection="row"
            marginBottom={25}
            marginHorizontal={10}
            marginTop={25}
            alignItems="center"
        >
            <Input
                ref={inputRef}
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Type a song name..."
                flex={1}
                onBlur={handleInputBlur}
                onEndEditing={handleInputEndEditing}
                blurOnSubmit={false}
                returnKeyType="search"
            />
            {searchQuery.length > 0 && (
                <Button marginLeft={10} onPress={handleCancel}>
                    Cancel
                </Button>
            )}
        </YStack>
    );
};

export default SearchBar;
